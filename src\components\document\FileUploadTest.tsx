import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import FileUpload from './FileUpload';
import { viewFile, downloadFile } from '@/utils/fileUtils';
import { Eye, Download, FileText } from 'lucide-react';

/**
 * Standalone component to test file upload and viewing functionality
 * This bypasses the form wizard for quick testing
 */
const FileUploadTest: React.FC = () => {
  const [currentFile, setCurrentFile] = useState<File | null>(null);
  const [fileData, setFileData] = useState<string>('');

  const handleFileChange = (file: File | null) => {
    setCurrentFile(file);
    if (file) {
      // Create object URL for the file
      const url = URL.createObjectURL(file);
      setFileData(url);
    } else {
      setFileData('');
    }
  };

  const handleView = () => {
    if (fileData && currentFile) {
      viewFile(fileData, currentFile.name);
    }
  };

  const handleDownload = () => {
    if (fileData && currentFile) {
      downloadFile(fileData, currentFile.name);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            File Upload & Viewing Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Upload a Test File</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Upload any supported file to test the new viewing functionality.
            </p>
            <FileUpload
              currentFile={currentFile}
              onFileChange={handleFileChange}
            />
          </div>

          {currentFile && fileData && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">File Details</h3>
              <div className="p-4 bg-muted rounded-lg space-y-2">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium">Name:</span>
                    <span className="ml-2">{currentFile.name}</span>
                  </div>
                  <div>
                    <span className="font-medium">Size:</span>
                    <span className="ml-2">{formatFileSize(currentFile.size)}</span>
                  </div>
                  <div>
                    <span className="font-medium">Type:</span>
                    <span className="ml-2">{currentFile.type}</span>
                  </div>
                  <div>
                    <span className="font-medium">Last Modified:</span>
                    <span className="ml-2">{new Date(currentFile.lastModified).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-medium">Test Actions</h3>
                <div className="flex gap-3">
                  <Button
                    onClick={handleView}
                    className="flex-1"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    View File
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleDownload}
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download File
                  </Button>
                </div>
                <p className="text-xs text-muted-foreground">
                  Click "View File" to test the new viewing functionality. 
                  The file should open in a new tab instead of downloading.
                </p>
              </div>
            </div>
          )}

          <div className="p-4 bg-blue-50 rounded-lg">
            <h4 className="font-medium text-blue-900 mb-2">Instructions:</h4>
            <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
              <li>Upload a file using the upload area above</li>
              <li>Click "View File" to test the new viewing behavior</li>
              <li>Click "Download File" to test the download functionality</li>
              <li>Try different file types (PDF, images, documents) to see different behaviors</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FileUploadTest;
