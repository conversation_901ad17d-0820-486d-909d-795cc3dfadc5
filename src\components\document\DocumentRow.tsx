import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { useDocuments } from '@/contexts/DocumentContext';
import { Document, STATUS_COLORS } from '@/models/document';
import { ColumnConfig } from './ColumnVisibilityControl';
import {
  Edit,
  Trash2,
  Download,
  MoreHorizontal,
  Paperclip,
  Shield,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import { viewFile, downloadFile } from '@/utils/fileUtils';

interface DocumentRowProps {
  document: Document;
  visibleColumns?: ColumnConfig[];
}

const DocumentRow: React.FC<DocumentRowProps> = ({ document, visibleColumns }) => {
  const { deleteDocument, setCurrentEditId } = useDocuments();

  const handleEdit = () => {
    setCurrentEditId(document.id);
    // Scroll to form
    const formElement = window.document.querySelector('[data-document-form]');
    if (formElement) {
      formElement.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const handleDelete = () => {
    deleteDocument(document.id);
  };

  const handleView = () => {
    if (document.fileData && document.fileName) {
      viewFile(document.fileData, document.fileName);
    }
  };

  const handleDownload = () => {
    if (document.fileData && document.fileName) {
      downloadFile(document.fileData, document.fileName);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy');
    } catch {
      return dateString;
    }
  };

  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case 'Public':
        return 'bg-green-100 text-green-800';
      case 'Internal':
        return 'bg-yellow-100 text-yellow-800';
      case 'Confidential':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const renderCell = (columnKey: keyof Document) => {
    switch (columnKey) {
      case 'documentReferenceNumber':
        return (
          <td className="px-4 py-3">
            <code className="text-sm bg-muted px-2 py-1 rounded">
              {document.documentReferenceNumber}
            </code>
          </td>
        );
      case 'documentTitle':
        return (
          <td className="px-4 py-3">
            <div className="font-medium">{document.documentTitle}</div>
          </td>
        );
      case 'version':
        return (
          <td className="px-4 py-3">
            <Badge variant="outline">v{document.version}</Badge>
          </td>
        );
      case 'issueDate':
        return (
          <td className="px-4 py-3">
            <span className="text-sm">{formatDate(document.issueDate)}</span>
          </td>
        );
      case 'authorFunction':
        return (
          <td className="px-4 py-3">
            <span className="text-sm">{document.authorFunction}</span>
          </td>
        );
      case 'responsibleDepartment':
        return (
          <td className="px-4 py-3">
            <span className="text-sm">{document.responsibleDepartment}</span>
          </td>
        );
      case 'status':
        return (
          <td className="px-4 py-3">
            <Badge
              variant="secondary"
              className={STATUS_COLORS[document.status]}
            >
              {document.status}
            </Badge>
          </td>
        );
      case 'storageLocation':
        return (
          <td className="px-4 py-3">
            <span className="text-sm">{document.storageLocation}</span>
          </td>
        );
      case 'approvalDate':
        return (
          <td className="px-4 py-3">
            <span className="text-sm">
              {document.approvalDate ? formatDate(document.approvalDate) : '-'}
            </span>
          </td>
        );
      case 'approvedByFunction':
        return (
          <td className="px-4 py-3">
            <span className="text-sm">{document.approvedByFunction || '-'}</span>
          </td>
        );
      case 'accessLevel':
        return (
          <td className="px-4 py-3">
            <Badge
              variant="outline"
              className={getAccessLevelColor(document.accessLevel)}
            >
              <Shield className="h-3 w-3 mr-1" />
              {document.accessLevel}
            </Badge>
          </td>
        );
      default:
        return (
          <td className="px-4 py-3">
            <span className="text-sm">{document[columnKey] as string || '-'}</span>
          </td>
        );
    }
  };

  return (
    <tr className="border-b hover:bg-muted/50">
      {/* Render visible columns dynamically */}
      {visibleColumns?.map((column) => (
        <React.Fragment key={column.key}>
          {renderCell(column.key)}
        </React.Fragment>
      ))}

      {/* If no visibleColumns provided, render all columns (backward compatibility) */}
      {!visibleColumns && (
        <>
          {renderCell('documentReferenceNumber')}
          {renderCell('documentTitle')}
          {renderCell('version')}
          {renderCell('issueDate')}
          {renderCell('authorFunction')}
          {renderCell('responsibleDepartment')}
          {renderCell('status')}
          {renderCell('storageLocation')}
          {renderCell('approvalDate')}
          {renderCell('approvedByFunction')}
          {renderCell('accessLevel')}
        </>
      )}

      {/* Attachment */}
      <td className="px-4 py-3">
        {document.fileName ? (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleView}
            className="h-8 w-8 p-0"
            title={`View ${document.fileName}`}
          >
            <Paperclip className="h-4 w-4" />
          </Button>
        ) : (
          <span className="text-muted-foreground text-sm">No file</span>
        )}
      </td>

      {/* Actions */}
      <td className="px-4 py-3">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </DropdownMenuItem>
            {document.fileName && (
              <>
                <DropdownMenuItem onClick={handleView}>
                  <Eye className="h-4 w-4 mr-2" />
                  View
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleDownload}>
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </DropdownMenuItem>
              </>
            )}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <DropdownMenuItem
                  onSelect={(e) => e.preventDefault()}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Document</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to delete "{document.documentTitle}"?
                    This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction
                    onClick={handleDelete}
                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                  >
                    Delete
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </DropdownMenuContent>
        </DropdownMenu>
      </td>
    </tr>
  );
};

export default DocumentRow;
