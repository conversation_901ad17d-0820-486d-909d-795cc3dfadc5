import React from 'react';
import { DocumentProvider } from '@/contexts/DocumentContext';
import DocumentForm from '@/components/document/DocumentForm';
import SearchFilters from '@/components/document/SearchFilters';
import DocumentTable from '@/components/document/DocumentTable';
import ExportControls from '@/components/document/ExportControls';
import FileUploadTest from '@/components/document/FileUploadTest';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { FileText, Info } from 'lucide-react';

const DocumentManagement: React.FC = () => {
  return (
    <DocumentProvider>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Document Management</h1>
            <p className="text-muted-foreground">
              Manage your HACCP documents, procedures, and records
            </p>
          </div>
        </div>

        {/* Welcome Message */}
        <Card className="border-blue-200 bg-blue-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Info className="h-5 w-5" />
              Welcome to Document Management
            </CardTitle>
          </CardHeader>
          <CardContent className="text-blue-700">
            <p className="mb-2">
              This system helps you manage all your HACCP-related documents including procedures, 
              processes, records, manuals, and software documentation.
            </p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Add new documents with proper categorization and version control</li>
              <li>Search and filter documents by type, status, or content</li>
              <li>Track document versions and effective dates</li>
              <li>Attach files and manage document lifecycle</li>
              <li>Export data for reporting and compliance purposes</li>
            </ul>
          </CardContent>
        </Card>

        {/* File Upload Test Component - Temporary for Testing */}
        <FileUploadTest />

        {/* Document Form */}
        <div data-document-form>
          <DocumentForm />
        </div>

        {/* Search and Filters */}
        <SearchFilters />

        {/* Document Table */}
        <DocumentTable />

        {/* Export Controls */}
        <ExportControls />

        {/* Help Section */}
        <Card className="border-gray-200 bg-gray-50/50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-gray-800">
              <FileText className="h-5 w-5" />
              Document Reference Format
            </CardTitle>
          </CardHeader>
          <CardContent className="text-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Reference ID Format:</h4>
                <p className="text-sm mb-2">Use the format: <code className="bg-gray-200 px-1 rounded">XX.XX.01</code></p>
                <ul className="text-sm space-y-1">
                  <li><strong>PR</strong> - Procédure</li>
                  <li><strong>PS</strong> - Processus</li>
                  <li><strong>EN</strong> - Enregistrement</li>
                  <li><strong>MN</strong> - Manuel</li>
                  <li><strong>LG</strong> - Logiciel</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">Version Format:</h4>
                <p className="text-sm mb-2">Use 2-digit format: <code className="bg-gray-200 px-1 rounded">00, 01, 02...</code></p>
                <ul className="text-sm space-y-1">
                  <li><strong>00</strong> - Initial version</li>
                  <li><strong>01</strong> - First revision</li>
                  <li><strong>02</strong> - Second revision</li>
                  <li>And so on...</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DocumentProvider>
  );
};

export default DocumentManagement;
